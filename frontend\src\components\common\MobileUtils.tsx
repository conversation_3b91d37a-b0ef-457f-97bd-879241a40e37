import React from 'react';

// Mobile-friendly container component
interface MobileContainerProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'sm' | 'md' | 'lg';
}

export const MobileContainer: React.FC<MobileContainerProps> = ({ 
  children, 
  className = '', 
  padding = 'md' 
}) => {
  const paddingClasses = {
    sm: 'p-2 sm:p-3',
    md: 'p-3 sm:p-4 md:p-6',
    lg: 'p-4 sm:p-6 md:p-8'
  };

  return (
    <div className={`mobile-container ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  );
};

// Mobile-friendly card component
interface MobileCardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
}

export const MobileCard: React.FC<MobileCardProps> = ({ 
  children, 
  className = '', 
  onClick,
  hoverable = false 
}) => {
  const baseClasses = 'mobile-card-enhanced bg-white dark:bg-dark-secondary border border-amspm-light-gray dark:border-dark-border';
  const hoverClasses = hoverable ? 'hover:shadow-md hover:border-amspm-primary dark:hover:border-dark-accent transition-all duration-200 cursor-pointer' : '';
  const clickClasses = onClick ? 'mobile-touch-target' : '';

  return (
    <div 
      className={`${baseClasses} ${hoverClasses} ${clickClasses} ${className}`}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

// Mobile-friendly button group
interface MobileButtonGroupProps {
  children: React.ReactNode;
  direction?: 'horizontal' | 'vertical' | 'responsive';
  className?: string;
}

export const MobileButtonGroup: React.FC<MobileButtonGroupProps> = ({ 
  children, 
  direction = 'responsive',
  className = '' 
}) => {
  const directionClasses = {
    horizontal: 'flex flex-row gap-2 sm:gap-3',
    vertical: 'flex flex-col gap-2',
    responsive: 'btn-group-mobile'
  };

  return (
    <div className={`${directionClasses[direction]} ${className}`}>
      {children}
    </div>
  );
};

// Mobile-friendly form group
interface MobileFormGroupProps {
  label: string;
  children: React.ReactNode;
  error?: string;
  required?: boolean;
  className?: string;
}

export const MobileFormGroup: React.FC<MobileFormGroupProps> = ({ 
  label, 
  children, 
  error, 
  required = false,
  className = '' 
}) => {
  return (
    <div className={`mobile-form-group ${className}`}>
      <label className="mobile-form-group label block text-sm font-medium text-amspm-text dark:text-dark-text-light mb-2">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children}
      {error && (
        <p className="text-red-600 dark:text-red-400 text-sm mt-1">{error}</p>
      )}
    </div>
  );
};

// Mobile-friendly input component
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
}

export const MobileInput: React.FC<MobileInputProps> = ({ 
  className = '', 
  error = false, 
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <input
      className={`mobile-form-input mobile-touch-target mobile-focus-visible ${errorClasses} ${className}`}
      {...props}
    />
  );
};

// Mobile-friendly select component
interface MobileSelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  error?: boolean;
  options: { value: string; label: string }[];
  placeholder?: string;
}

export const MobileSelect: React.FC<MobileSelectProps> = ({ 
  className = '', 
  error = false,
  options,
  placeholder,
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <select
      className={`mobile-form-input mobile-touch-target mobile-focus-visible ${errorClasses} ${className}`}
      {...props}
    >
      {placeholder && <option value="">{placeholder}</option>}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// Mobile-friendly textarea component
interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
}

export const MobileTextarea: React.FC<MobileTextareaProps> = ({ 
  className = '', 
  error = false, 
  ...props 
}) => {
  const errorClasses = error ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';
  
  return (
    <textarea
      className={`mobile-form-input mobile-touch-target mobile-focus-visible resize-none ${errorClasses} ${className}`}
      rows={4}
      {...props}
    />
  );
};

// Mobile-friendly search component
interface MobileSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onClear?: () => void;
  className?: string;
}

export const MobileSearch: React.FC<MobileSearchProps> = ({ 
  value, 
  onChange, 
  placeholder = "Search...",
  onClear,
  className = '' 
}) => {
  return (
    <div className={`mobile-search-container ${className}`}>
      <div className="mobile-search-icon">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="mobile-search-input mobile-touch-target mobile-focus-visible"
      />
      {value && onClear && (
        <button
          onClick={onClear}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

// Mobile-friendly loading component
interface MobileLoadingProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const MobileLoading: React.FC<MobileLoadingProps> = ({ 
  message = "Loading...",
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className="mobile-loading">
      <div className={`mobile-loading-spinner ${sizeClasses[size]}`}></div>
      <p className="text-sm text-amspm-text-light dark:text-dark-text-light">{message}</p>
    </div>
  );
};

// Mobile-friendly message component
interface MobileMessageProps {
  type: 'error' | 'success' | 'warning' | 'info';
  message: string;
  onClose?: () => void;
}

export const MobileMessage: React.FC<MobileMessageProps> = ({ 
  type, 
  message, 
  onClose 
}) => {
  const typeClasses = {
    error: 'mobile-message-error',
    success: 'mobile-message-success',
    warning: 'bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-200 dark:border-yellow-800/30',
    info: 'bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-200 dark:border-blue-800/30'
  };

  return (
    <div className={`mobile-message ${typeClasses[type]} flex items-center justify-between`}>
      <span>{message}</span>
      {onClose && (
        <button
          onClick={onClose}
          className="ml-2 text-current opacity-70 hover:opacity-100 mobile-touch-target"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}
    </div>
  );
};

// Mobile-friendly page header
interface MobilePageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  backButton?: boolean;
  onBack?: () => void;
}

export const MobilePageHeader: React.FC<MobilePageHeaderProps> = ({ 
  title, 
  subtitle, 
  actions,
  backButton = false,
  onBack 
}) => {
  return (
    <div className="mobile-header">
      <div className="flex items-center gap-3">
        {backButton && (
          <button
            onClick={onBack}
            className="mobile-touch-target p-2 rounded-lg hover:bg-amspm-light-gray dark:hover:bg-dark-hover"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        <div>
          <h1 className="mobile-header-title">{title}</h1>
          {subtitle && (
            <p className="text-sm text-amspm-text-light dark:text-dark-text-light mt-1">{subtitle}</p>
          )}
        </div>
      </div>
      {actions && (
        <div className="mobile-header-actions">
          {actions}
        </div>
      )}
    </div>
  );
};
