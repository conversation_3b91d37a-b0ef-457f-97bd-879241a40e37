/* Mobile-specific enhancements for AMSPM Customer Management */

/* Mobile viewport and safe areas */
@supports (padding: max(0px)) {
  .mobile-safe-area {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }
}

/* Mobile-first responsive breakpoints */
@media (max-width: 640px) {
  /* Enhanced mobile containers */
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  /* Mobile-friendly headers */
  .mobile-page-header {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid var(--amspm-light-gray);
  }
  
  .dark .mobile-page-header {
    border-bottom-color: var(--dark-border);
  }
  
  /* Mobile navigation improvements */
  .mobile-nav-toggle {
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .mobile-nav-toggle:hover {
    background-color: var(--amspm-light-gray);
  }
  
  .dark .mobile-nav-toggle:hover {
    background-color: var(--dark-hover);
  }
  
  /* Mobile table improvements */
  .mobile-table-container {
    margin: 0 -0.75rem;
    padding: 0 0.75rem;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Mobile card enhancements */
  .mobile-card-enhanced {
    margin-bottom: 0.75rem;
    padding: 0.875rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }
  
  .mobile-card-enhanced:last-child {
    margin-bottom: 0;
  }
  
  /* Mobile form improvements */
  .mobile-form-group {
    margin-bottom: 1rem;
  }
  
  .mobile-form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.375rem;
    display: block;
  }
  
  .mobile-form-input {
    width: 100%;
    padding: 0.75rem;
    font-size: 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    min-height: 44px;
    -webkit-appearance: none;
    appearance: none;
  }
  
  .dark .mobile-form-input {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }
  
  .mobile-form-input:focus {
    outline: none;
    border-color: var(--amspm-primary);
    box-shadow: 0 0 0 3px rgba(0, 48, 135, 0.1);
  }
  
  .dark .mobile-form-input:focus {
    border-color: var(--dark-accent);
    box-shadow: 0 0 0 3px rgba(26, 117, 255, 0.2);
  }
  
  /* Mobile button improvements */
  .mobile-btn-stack {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
  }
  
  .mobile-btn-stack .btn {
    width: 100%;
    justify-content: center;
  }
  
  /* Mobile modal improvements */
  .mobile-modal-overlay {
    padding: 0;
    align-items: flex-end;
  }

  .mobile-modal-content {
    width: 100%;
    max-width: none;
    margin: 0;
    border-radius: 1rem 1rem 0 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-height: 90vh;
    animation: slideUpIn 0.3s ease-out;
    padding: 1rem;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Handle for mobile modal */
  .mobile-modal-content::before {
    content: '';
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
    height: 0.25rem;
    background-color: var(--amspm-light-gray);
    border-radius: 0.125rem;
  }

  .dark .mobile-modal-content::before {
    background-color: var(--dark-border);
  }

  @keyframes slideUpIn {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  /* Mobile modal form improvements */
  .mobile-modal-content .form-actions {
    position: sticky;
    bottom: -1rem;
    left: -1rem;
    right: -1rem;
    background: white;
    border-top: 1px solid var(--amspm-light-gray);
    padding: 1rem;
    margin: 1rem -1rem -1rem -1rem;
    border-radius: 0;
  }

  .dark .mobile-modal-content .form-actions {
    background: var(--dark-secondary);
    border-top-color: var(--dark-border);
  }
  
  /* Mobile search improvements */
  .mobile-search-container {
    position: relative;
    margin-bottom: 1rem;
  }
  
  .mobile-search-input {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    font-size: 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
  }
  
  .dark .mobile-search-input {
    background-color: var(--dark-input);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }
  
  .mobile-search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--amspm-text-light);
  }
  
  .dark .mobile-search-icon {
    color: var(--dark-text-light);
  }
  
  /* Mobile pagination improvements */
  .mobile-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0;
  }
  
  .mobile-pagination-btn {
    padding: 0.5rem 0.75rem;
    min-width: 44px;
    min-height: 44px;
    border-radius: 0.375rem;
    border: 1px solid var(--amspm-light-gray);
    background-color: white;
    color: var(--amspm-text);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .dark .mobile-pagination-btn {
    background-color: var(--dark-secondary);
    border-color: var(--dark-border);
    color: var(--dark-text);
  }
  
  .mobile-pagination-btn:hover {
    background-color: var(--amspm-light-gray);
  }
  
  .dark .mobile-pagination-btn:hover {
    background-color: var(--dark-hover);
  }
  
  .mobile-pagination-btn.active {
    background-color: var(--amspm-primary);
    color: white;
    border-color: var(--amspm-primary);
  }
  
  .dark .mobile-pagination-btn.active {
    background-color: var(--dark-accent);
    border-color: var(--dark-accent);
  }
  
  /* Mobile loading improvements */
  .mobile-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    text-align: center;
  }
  
  .mobile-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--amspm-light-gray);
    border-top: 2px solid var(--amspm-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  .dark .mobile-loading-spinner {
    border-color: var(--dark-border);
    border-top-color: var(--dark-accent);
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  /* Mobile error/success messages */
  .mobile-message {
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
  
  .mobile-message-error {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }
  
  .dark .mobile-message-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: #fca5a5;
    border-color: rgba(239, 68, 68, 0.3);
  }
  
  .mobile-message-success {
    background-color: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
  }
  
  .dark .mobile-message-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: #6ee7b7;
    border-color: rgba(16, 185, 129, 0.3);
  }
  
  /* Mobile accessibility improvements */
  .mobile-focus-visible:focus-visible {
    outline: 2px solid var(--amspm-primary);
    outline-offset: 2px;
  }
  
  .dark .mobile-focus-visible:focus-visible {
    outline-color: var(--dark-accent);
  }
  
  /* Mobile touch improvements */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Mobile scroll improvements */
  .mobile-scroll-container {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }
  
  .mobile-scroll-container::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
  
  .mobile-scroll-container::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .mobile-scroll-container::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 2px;
  }
  
  .dark .mobile-scroll-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* Tablet-specific improvements */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .tablet-modal-content {
    max-width: 90%;
    margin: 2rem auto;
  }
}
